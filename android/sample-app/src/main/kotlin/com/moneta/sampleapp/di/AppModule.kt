package com.moneta.sampleapp.di

import android.content.Context
import com.moneta.sampleapp.data.repository.MonetaRepository
import com.moneta.sampleapp.services.FirebaseManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object AppModule {
    
    @Provides
    @Singleton
    fun provideMonetaRepository(): MonetaRepository {
        return MonetaRepository()
    }

    @Provides
    @Singleton
    fun provideFirebaseManager(@ApplicationContext context: Context): FirebaseManager {
        return FirebaseManager(context)
    }
}
