package com.moneta.sampleapp.data.repository

import com.moneta.sdk.MonetaSDK
import com.moneta.sdk.model.*
import com.moneta.sdk.util.MonetaException
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class MonetaRepository @Inject constructor() {
    
    private val sdk = MonetaSDK.shared
    
    // Onboarding
    suspend fun startOnboarding(): Result<OnboardVerificationResponse> = try {
        Result.success(sdk.startOnboarding())
    } catch (e: MonetaException) {
        Result.failure(e)
    } catch (e: Exception) {
        Result.failure(MonetaException.UnknownException(e.message ?: "Unknown error"))
    }
    
    suspend fun completeOnboarding(): Result<OnboardUserResponse> = try {
        Result.success(sdk.completeOnboarding())
    } catch (e: MonetaException) {
        Result.failure(e)
    } catch (e: Exception) {
        Result.failure(MonetaException.UnknownException(e.message ?: "Unknown error"))
    }

    suspend fun startOnboardingWithQRCode(qrCode: String, deviceToken: String): Result<OnboardVerificationResponse> = try {
        Result.success(sdk.startOnboardingWithQRCode(qrCode, deviceToken))
    } catch (e: MonetaException) {
        Result.failure(e)
    } catch (e: Exception) {
        Result.failure(MonetaException.UnknownException(e.message ?: "Unknown error"))
    }
    
    // Transactions
    suspend fun getTransactions(page: Int = 0, size: Int = 20): Result<PaginatedResponse<TransactionResponse>> = try {
        Result.success(sdk.getTransactions(page, size))
    } catch (e: MonetaException) {
        Result.failure(e)
    } catch (e: Exception) {
        Result.failure(MonetaException.UnknownException(e.message ?: "Unknown error"))
    }
    
    // Balance
    suspend fun getBalance(): Result<UserBalanceResponse> = try {
        Result.success(sdk.getBalance())
    } catch (e: MonetaException) {
        Result.failure(e)
    } catch (e: Exception) {
        Result.failure(MonetaException.UnknownException(e.message ?: "Unknown error"))
    }
    
    // Recommendations
    suspend fun getRecommendations(
        userId: String,
        userProfile: UserProfile,
        offset: Int = 0,
        limit: Int = 10
    ): Result<List<Any>> = try {
        Result.success(sdk.getRecommendations(userId, userProfile, offset, limit))
    } catch (e: MonetaException) {
        Result.failure(e)
    } catch (e: Exception) {
        Result.failure(MonetaException.UnknownException(e.message ?: "Unknown error"))
    }
    
    // Publisher Authentication
    suspend fun authPublisher(qrCodeId: String, session: String): Result<OnboardUserResponse> = try {
        Result.success(sdk.authPublisherLegacy(qrCodeId, session))
    } catch (e: MonetaException) {
        Result.failure(e)
    } catch (e: Exception) {
        Result.failure(MonetaException.UnknownException(e.message ?: "Unknown error"))
    }
    
    // User Profile
    suspend fun updateUserProfile(
        userId: String,
        contentPreferences: UAUserContentPreferences
    ): Result<Boolean> = try {
        Result.success(sdk.updateUserProfile(userId, contentPreferences))
    } catch (e: MonetaException) {
        Result.failure(e)
    } catch (e: Exception) {
        Result.failure(MonetaException.UnknownException(e.message ?: "Unknown error"))
    }
    
    // Flow-based methods for reactive UI
    fun getTransactionsFlow(page: Int = 0, size: Int = 20): Flow<Result<PaginatedResponse<TransactionResponse>>> = flow {
        emit(getTransactions(page, size))
    }
    
    fun getBalanceFlow(): Flow<Result<UserBalanceResponse>> = flow {
        emit(getBalance())
    }
    
    fun getRecommendationsFlow(
        userId: String,
        userProfile: UserProfile,
        offset: Int = 0,
        limit: Int = 10
    ): Flow<Result<List<Any>>> = flow {
        emit(getRecommendations(userId, userProfile, offset, limit))
    }
}
