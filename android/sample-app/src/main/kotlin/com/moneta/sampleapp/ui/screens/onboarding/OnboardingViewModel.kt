package com.moneta.sampleapp.ui.screens.onboarding

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.moneta.sampleapp.data.repository.MonetaRepository
import com.moneta.sampleapp.services.FirebaseManager
import com.moneta.sdk.model.OnboardUserResponse
import com.moneta.sdk.model.OnboardVerificationResponse
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class OnboardingUiState(
    val isLoading: Boolean = false,
    val onboardingData: OnboardVerificationResponse? = null,
    val completedUser: OnboardUserResponse? = null,
    val error: String? = null,
    val isOnboardingStarted: Boolean = false,
    val isOnboardingCompleted: Boolean = false,
    val isQRScannerActive: Boolean = false,
    val scannedQRCode: String? = null
)

@HiltViewModel
class OnboardingViewModel @Inject constructor(
    private val repository: MonetaRepository,
    private val firebaseManager: FirebaseManager
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(OnboardingUiState())
    val uiState: StateFlow<OnboardingUiState> = _uiState.asStateFlow()
    
    fun startOnboarding() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            repository.startOnboarding().fold(
                onSuccess = { onboardingData ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        onboardingData = onboardingData,
                        isOnboardingStarted = true
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message ?: "Failed to start onboarding"
                    )
                }
            )
        }
    }
    
    fun completeOnboarding() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            repository.completeOnboarding().fold(
                onSuccess = { userData ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        completedUser = userData,
                        isOnboardingCompleted = true
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message ?: "Failed to complete onboarding"
                    )
                }
            )
        }
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun startQRScanning() {
        _uiState.value = _uiState.value.copy(
            isQRScannerActive = true,
            error = null
        )
    }

    fun stopQRScanning() {
        _uiState.value = _uiState.value.copy(isQRScannerActive = false)
    }

    fun onQRCodeScanned(qrCode: String) {
        _uiState.value = _uiState.value.copy(
            isQRScannerActive = false,
            scannedQRCode = qrCode
        )

        // Start onboarding with the scanned QR code
        startOnboardingWithQRCode(qrCode)
    }

    private fun startOnboardingWithQRCode(qrCode: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            try {
                // Get device token from Firebase
                val deviceTokenResult = firebaseManager.getDeviceToken()
                deviceTokenResult.fold(
                    onSuccess = { deviceToken ->
                        // Call repository with QR code and device token
                        repository.startOnboardingWithQRCode(qrCode, deviceToken).fold(
                            onSuccess = { onboardingData ->
                                _uiState.value = _uiState.value.copy(
                                    isLoading = false,
                                    onboardingData = onboardingData,
                                    isOnboardingStarted = true
                                )
                            },
                            onFailure = { exception ->
                                _uiState.value = _uiState.value.copy(
                                    isLoading = false,
                                    error = exception.message ?: "Failed to start onboarding with QR code"
                                )
                            }
                        )
                    },
                    onFailure = { exception ->
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = "Failed to get device token: ${exception.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Unexpected error: ${e.message}"
                )
            }
        }
    }
}
