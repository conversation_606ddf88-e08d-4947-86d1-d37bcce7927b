package com.moneta.sampleapp.services

import android.util.Log
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage

class MonetaFirebaseMessagingService : FirebaseMessagingService() {

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        Log.d(TAG, "Refreshed token: $token")
        
        // Store the token for later use
        // In a real app, you might want to send this token to your server
        storeTokenLocally(token)
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        Log.d(TAG, "From: ${remoteMessage.from}")

        // Check if message contains a data payload
        if (remoteMessage.data.isNotEmpty()) {
            Log.d(TAG, "Message data payload: ${remoteMessage.data}")
        }

        // Check if message contains a notification payload
        remoteMessage.notification?.let {
            Log.d(TAG, "Message Notification Body: ${it.body}")
        }
    }

    private fun storeTokenLocally(token: String) {
        val sharedPrefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE)
        sharedPrefs.edit()
            .putString(TOKEN_KEY, token)
            .apply()
        Log.d(TAG, "Token stored locally")
    }

    companion object {
        private const val TAG = "MonetaFCMService"
        const val PREFS_NAME = "moneta_fcm_prefs"
        const val TOKEN_KEY = "fcm_token"
        
        fun getStoredToken(context: android.content.Context): String? {
            val sharedPrefs = context.getSharedPreferences(PREFS_NAME, android.content.Context.MODE_PRIVATE)
            return sharedPrefs.getString(TOKEN_KEY, null)
        }
    }
}
