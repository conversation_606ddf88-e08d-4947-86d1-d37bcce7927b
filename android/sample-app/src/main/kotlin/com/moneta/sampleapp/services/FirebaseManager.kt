package com.moneta.sampleapp.services

import android.content.Context
import android.util.Log
import com.google.firebase.messaging.FirebaseMessaging
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FirebaseManager @Inject constructor(
    private val context: Context
) {
    
    suspend fun getDeviceToken(): Result<String> {
        return try {
            // First try to get stored token
            val storedToken = MonetaFirebaseMessagingService.getStoredToken(context)
            if (!storedToken.isNullOrEmpty()) {
                Log.d(TAG, "Using stored FCM token")
                return Result.success(storedToken)
            }
            
            // If no stored token, get fresh token from Firebase
            Log.d(TAG, "Fetching fresh FCM token")
            val token = FirebaseMessaging.getInstance().token.await()
            Log.d(TAG, "Fresh FCM token received: $token")
            
            // Store the token for future use
            storeToken(token)
            
            Result.success(token)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get FCM token", e)
            Result.failure(e)
        }
    }
    
    private fun storeToken(token: String) {
        val sharedPrefs = context.getSharedPreferences(
            MonetaFirebaseMessagingService.PREFS_NAME, 
            Context.MODE_PRIVATE
        )
        sharedPrefs.edit()
            .putString(MonetaFirebaseMessagingService.TOKEN_KEY, token)
            .apply()
    }
    
    companion object {
        private const val TAG = "FirebaseManager"
    }
}
