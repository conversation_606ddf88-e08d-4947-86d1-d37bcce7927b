package com.moneta.sampleapp.ui.screens.onboarding

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.activity.compose.rememberLauncherForActivityResult
import com.journeyapps.barcodescanner.ScanContract
import com.journeyapps.barcodescanner.ScanOptions
import com.moneta.sampleapp.R
import com.moneta.sampleapp.ui.components.ErrorDialog
import com.moneta.sampleapp.ui.components.LoadingIndicator

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OnboardingScreen(
    onOnboardingComplete: () -> Unit,
    viewModel: OnboardingViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    // QR Code scanner launcher
    val scanLauncher = rememberLauncherForActivityResult(
        contract = ScanContract()
    ) { result ->
        viewModel.stopQRScanning()
        if (result.contents != null) {
            viewModel.onQRCodeScanned(result.contents)
        }
    }

    // Launch QR scanner when requested
    LaunchedEffect(uiState.isQRScannerActive) {
        if (uiState.isQRScannerActive) {
            val options = ScanOptions().apply {
                setPrompt("Scan the QR code for onboarding")
                setBeepEnabled(true)
                setOrientationLocked(false)
                setCaptureActivity(com.journeyapps.barcodescanner.CaptureActivity::class.java)
            }
            scanLauncher.launch(options)
        }
    }

    // Navigate to dashboard when onboarding is completed
    LaunchedEffect(uiState.isOnboardingCompleted) {
        if (uiState.isOnboardingCompleted) {
            onOnboardingComplete()
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // Title
        Text(
            text = stringResource(R.string.onboarding_title),
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Subtitle
        Text(
            text = stringResource(R.string.onboarding_subtitle),
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        when {
            uiState.isLoading -> {
                LoadingIndicator()
            }
            
            uiState.onboardingData != null && !uiState.isOnboardingCompleted -> {
                // Show onboarding data
                OnboardingDataCard(
                    userCode = uiState.onboardingData!!.userCode,
                    deviceCode = uiState.onboardingData!!.deviceCode,
                    onCompleteOnboarding = viewModel::completeOnboarding
                )
            }
            
            else -> {
                // Start onboarding button - now opens QR scanner
                Button(
                    onClick = viewModel::startQRScanning,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp)
                ) {
                    Text(
                        text = stringResource(R.string.scan_qr_code),
                        style = MaterialTheme.typography.titleMedium
                    )
                }
            }
        }
    }
    
    // Error dialog
    uiState.error?.let { error ->
        ErrorDialog(
            message = error,
            onDismiss = viewModel::clearError
        )
    }
}

@Composable
private fun OnboardingDataCard(
    userCode: String,
    deviceCode: String,
    onCompleteOnboarding: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(R.string.onboarding_instructions),
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // User Code
            Text(
                text = stringResource(R.string.user_code_label),
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Text(
                text = userCode,
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Device Code (smaller, for reference)
            Text(
                text = stringResource(R.string.device_code_label),
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Text(
                text = deviceCode,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            Button(
                onClick = onCompleteOnboarding,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(stringResource(R.string.complete_onboarding))
            }
        }
    }
}
