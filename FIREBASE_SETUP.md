# Firebase Setup for Moneta Mobile SDKs

This document explains how to set up Firebase Cloud Messaging (FCM) for the Moneta Mobile SDKs sample applications.

## Prerequisites

1. A Firebase project (create one at https://console.firebase.google.com)
2. Android and/or iOS apps registered in your Firebase project

## Android Setup

### 1. Download Configuration File
1. In the Firebase Console, go to Project Settings
2. Under "Your apps", find your Android app
3. Download the `google-services.json` file
4. Replace the placeholder file at `android/sample-app/google-services.json` with your actual file

### 2. Package Name
Ensure your Firebase Android app is registered with the package name: `com.moneta.sampleapp`

### 3. Dependencies
The required Firebase dependencies are already added to the project:
- Firebase BOM
- Firebase Cloud Messaging

## iOS Setup

### 1. Download Configuration File
1. In the Firebase Console, go to Project Settings
2. Under "Your apps", find your iOS app
3. Download the `GoogleService-Info.plist` file
4. Replace the placeholder file at `ios/MonetaSample/MonetaSample/GoogleService-Info.plist` with your actual file

### 2. Bundle Identifier
Ensure your Firebase iOS app is registered with the bundle identifier: `com.moneta.sample.app.MonetaSample`

### 3. Add Firebase SDK
You need to add Firebase SDK to the iOS project via Swift Package Manager:

1. Open `ios/MonetaSample/MonetaSample.xcodeproj` in Xcode
2. Go to File → Add Package Dependencies
3. Enter the Firebase iOS SDK URL: `https://github.com/firebase/firebase-ios-sdk`
4. Select version 10.0.0 or later
5. Add the following products to your target:
   - FirebaseCore
   - FirebaseMessaging

### 4. APNs Configuration
For production use, you'll need to configure Apple Push Notification service (APNs):
1. Create an APNs certificate or key in Apple Developer Console
2. Upload it to Firebase Console under Project Settings → Cloud Messaging

## Testing

### Android
1. Build and run the sample app: `./gradlew :sample-app:assembleDebug`
2. Check the logs for "FCM Token" to see the device token

### iOS
1. Build and run the sample app in Xcode
2. Grant notification permissions when prompted
3. Check the console for "FCM Token" to see the device token

## Mock Implementation

If Firebase is not properly configured, the apps will fall back to mock device tokens for development purposes. These mock tokens will allow you to test the onboarding flow without a full Firebase setup.

## Troubleshooting

### Android
- Ensure `google-services.json` is in the correct location
- Verify the package name matches your Firebase configuration
- Check that Google Services plugin is applied

### iOS
- Ensure `GoogleService-Info.plist` is added to the Xcode project
- Verify the bundle identifier matches your Firebase configuration
- Make sure Firebase SDK packages are properly added
- Check that notification permissions are granted

## Security Note

The placeholder configuration files included in this repository contain dummy values and should never be used in production. Always replace them with your actual Firebase configuration files and keep them secure.
