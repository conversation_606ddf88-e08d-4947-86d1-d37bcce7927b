import SwiftUI
import MonetaSDK

struct OnboardingView: View {
    @StateObject private var viewModel = OnboardingViewModel()
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var firebaseManager: FirebaseManager
    @State private var showingQRScanner = false
    @State private var showingManualEntry = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    Spacer(minLength: 40)
                    
                    // App Icon and Title
                    VStack(spacing: 16) {
                        Image(systemName: "creditcard.circle.fill")
                            .font(.system(size: 80))
                            .foregroundColor(.blue)
                        
                        Text("Welcome to Moneta")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .multilineTextAlignment(.center)
                        
                        Text("Get started by setting up your account")
                            .font(.title3)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    
                    Spacer(minLength: 32)
                    
                    // Content based on state
                    if viewModel.isLoading {
                        ProgressView("Loading...")
                            .scaleEffect(1.2)
                            .padding()
                    } else if let onboardingData = viewModel.onboardingData {
                        OnboardingDataCard(
                            onboardingData: onboardingData,
                            onComplete: {
                                Task {
                                    await viewModel.completeOnboarding()
                                }
                            }
                        )
                    } else {
                        VStack(spacing: 16) {
                            Button(action: {
                                viewModel.startQRScanning()
                            }) {
                                Text("Scan QR Code")
                                    .font(.headline)
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .padding()
                                    .background(Color.blue)
                                    .cornerRadius(12)
                            }

                            #if os(macOS)
                            Button(action: {
                                viewModel.showManualEntry()
                            }) {
                                Text("Enter QR Code Manually")
                                    .font(.subheadline)
                                    .foregroundColor(.blue)
                            }
                            #endif
                        }
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 24)
            }
            .platformNavigationBarHidden(true)
        }
        .alert("Error", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("OK") {
                viewModel.clearError()
            }
        } message: {
            if let error = viewModel.errorMessage {
                Text(error)
            }
        }
        .onChange(of: viewModel.isOnboardingCompleted) { completed in
            if completed {
                appState.completeOnboarding()
            }
        }
        .onChange(of: viewModel.shouldShowQRScanner) { shouldShow in
            showingQRScanner = shouldShow
        }
        .onChange(of: viewModel.shouldShowManualEntry) { shouldShow in
            showingManualEntry = shouldShow
        }
        .sheet(isPresented: $showingQRScanner) {
            #if os(iOS)
            QRCodeScannerView { qrCode in
                showingQRScanner = false
                Task {
                    await viewModel.processQRCode(qrCode, firebaseManager: firebaseManager)
                }
            }
            #endif
        }
        .sheet(isPresented: $showingManualEntry) {
            ManualQREntryView { qrCode in
                showingManualEntry = false
                Task {
                    await viewModel.processQRCode(qrCode, firebaseManager: firebaseManager)
                }
            }
        }
    }
}

struct OnboardingDataCard: View {
    let onboardingData: OnboardVerificationResponse
    let onComplete: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Text("Complete Your Setup")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Please use this code to complete your onboarding on the web portal")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            VStack(spacing: 16) {
                VStack(spacing: 8) {
                    Text("Your User Code:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(onboardingData.userCode)
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                        .padding()
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(8)
                }
                
                VStack(spacing: 8) {
                    Text("Device Code:")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Text(onboardingData.deviceCode)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
            }
            
            Button(action: onComplete) {
                Text("Complete Onboarding")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.green)
                    .cornerRadius(12)
            }
        }
        .padding(24)
        .platformBackground()
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
    }
}

#Preview {
    OnboardingView()
        .environmentObject(AppState())
}
