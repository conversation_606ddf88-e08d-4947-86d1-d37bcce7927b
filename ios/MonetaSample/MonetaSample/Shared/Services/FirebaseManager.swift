import Foundation
import UserNotifications

#if canImport(FirebaseCore)
import FirebaseCore
#endif

#if canImport(FirebaseMessaging)
import FirebaseMessaging
#endif

@MainActor
class FirebaseManager: ObservableObject {
    static let shared = FirebaseManager()
    
    @Published var deviceToken: String?
    @Published var isInitialized = false
    
    private init() {}
    
    func initialize() {
        #if canImport(FirebaseCore)
        FirebaseApp.configure()
        isInitialized = true
        print("Firebase initialized successfully")
        #else
        print("Firebase not available - using mock implementation")
        isInitialized = true
        #endif
    }
    
    func requestNotificationPermissions() async -> Bool {
        let center = UNUserNotificationCenter.current()
        
        do {
            let granted = try await center.requestAuthorization(options: [.alert, .sound, .badge])
            if granted {
                print("Notification permissions granted")
                await MainActor.run {
                    UIApplication.shared.registerForRemoteNotifications()
                }
                return true
            } else {
                print("Notification permissions denied")
                return false
            }
        } catch {
            print("Error requesting notification permissions: \(error)")
            return false
        }
    }
    
    func getDeviceToken() async -> String? {
        #if canImport(FirebaseMessaging)
        // First check if we have a cached token
        if let cachedToken = deviceToken {
            return cachedToken
        }
        
        // Request permissions first
        let permissionGranted = await requestNotificationPermissions()
        guard permissionGranted else {
            print("Cannot get device token without notification permissions")
            return generateMockToken()
        }
        
        do {
            let token = try await Messaging.messaging().token()
            await MainActor.run {
                self.deviceToken = token
            }
            print("FCM Token: \(token)")
            return token
        } catch {
            print("Error getting FCM token: \(error)")
            return generateMockToken()
        }
        #else
        print("Firebase Messaging not available - using mock token")
        return generateMockToken()
        #endif
    }
    
    private func generateMockToken() -> String {
        // Generate a mock token for development/testing
        let mockToken = "mock_device_token_\(UUID().uuidString.prefix(8))"
        Task { @MainActor in
            self.deviceToken = mockToken
        }
        return mockToken
    }
    
    func handleRemoteNotificationRegistration(deviceToken: Data) {
        #if canImport(FirebaseMessaging)
        Messaging.messaging().apnsToken = deviceToken
        print("APNS token set for Firebase")
        #endif
    }
}
