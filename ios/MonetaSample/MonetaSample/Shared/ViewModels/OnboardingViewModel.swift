import Foundation
import MonetaSDK

@MainActor
class OnboardingViewModel: ObservableObject {
    @Published var isLoading = false
    @Published var onboardingData: OnboardVerificationResponse?
    @Published var isOnboardingCompleted = false
    @Published var errorMessage: String?
    @Published var shouldShowQRScanner = false
    @Published var shouldShowManualEntry = false
    
    func startOnboarding() async {
        isLoading = true
        errorMessage = nil
        
        do {
            let data = try await MonetaSDK.shared.startOnboarding()
            onboardingData = data
        } catch {
            errorMessage = "Failed to start onboarding: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    func completeOnboarding() async {
        isLoading = true
        errorMessage = nil
        
        do {
            _ = try await MonetaSDK.shared.completeOnboarding()
            isOnboardingCompleted = true
        } catch {
            errorMessage = "Failed to complete onboarding: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    func clearError() {
        errorMessage = nil
    }

    func startQRScanning() {
        shouldShowQRScanner = true
    }

    func showManualEntry() {
        shouldShowManualEntry = true
    }

    func processQRCode(_ qrCode: String, firebaseManager: FirebaseManager) async {
        isLoading = true
        errorMessage = nil

        do {
            // Get device token from Firebase
            guard let deviceToken = await firebaseManager.getDeviceToken() else {
                errorMessage = "Failed to get device token"
                isLoading = false
                return
            }

            // Call SDK with QR code and device token
            let data = try await MonetaSDK.shared.startOnboardingWithQRCode(qrCode: qrCode, deviceToken: deviceToken)
            onboardingData = data
        } catch {
            errorMessage = "Failed to start onboarding: \(error.localizedDescription)"
        }

        isLoading = false
        shouldShowQRScanner = false
        shouldShowManualEntry = false
    }
}
