import SwiftUI
import MonetaSDK

#if canImport(FirebaseCore)
import FirebaseCore
#endif

@main
struct MonetaSampleApp: App {
    @StateObject private var appState = AppState()
    @StateObject private var firebaseManager = FirebaseManager.shared

    init() {
        // Initialize Firebase
        firebaseManager.initialize()
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .preferredColorScheme(.light) // Force light mode for consistency
                .environmentObject(appState)
                .environmentObject(firebaseManager)
                .task {
                    await initializeSDK()
                }
                .onReceive(NotificationCenter.default.publisher(for: UIApplication.didRegisterForRemoteNotificationsNotification)) { notification in
                    if let deviceToken = notification.object as? Data {
                        firebaseManager.handleRemoteNotificationRegistration(deviceToken: deviceToken)
                    }
                }
        }
    }

    private func initializeSDK() async {
        // Initialize the Moneta SDK asynchronously
        // In a real app, you would get this URL from your configuration
        let baseUrl = "https://membership-portal.dev.pressingly.net"

        do {
            print("Starting SDK initialization...")
            try await MonetaSDK.shared.initialize(baseUrl: baseUrl)

            // SDK is now fully initialized
            print("SDK initialization successful!")
            print("Is fully initialized: \(MonetaSDK.shared.isSDKFullyInitialized())")

            // Access MO info after successful initialization
            if let currency = try? MonetaSDK.shared.getMoInfoCurrency() {
                print("MO Info - Currency: \(currency)")
            }

            if let region = try? MonetaSDK.shared.getMoInfoRegion() {
                print("MO Info - Region: \(region)")
            }

            // Update app state
            await MainActor.run {
                appState.isSDKInitialized = true
                appState.initializationError = nil
            }

        } catch {
            print("SDK initialization failed: \(error)")
            // Handle initialization failure
            await MainActor.run {
                appState.isSDKInitialized = false
                appState.initializationError = error
            }
        }
    }
}


